#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧食堂管理系统 - 主程序入口
Smart Canteen Management System - Main Entry Point
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ui.login_window import LoginWindow

def main():
    """主程序入口"""
    try:
        # 创建应用程序实例
        app = QApplication(sys.argv)

        # 设置应用程序属性
        app.setApplicationName("智慧食堂管理系统")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("智慧食堂")

        # 设置全局字体
        try:
            font = QFont("Microsoft YaHei", 10)
            app.setFont(font)
        except:
            # 如果字体设置失败，使用默认字体
            pass

        # PyQt6中高DPI缩放默认启用，无需手动设置

        # 检查是否有有效的认证信息
        from utils.auth_manager import auth_manager
        username, token = auth_manager.get_current_auth()

        if username and token and auth_manager.is_token_valid():
            # 有有效token，直接进入主界面
            print(f"检测到有效token，用户: {username}")
            from ui.main_window import MainWindow
            main_window = MainWindow(token)
            main_window.show()
        else:
            # 没有有效token，显示登录窗口
            if username and not auth_manager.is_token_valid():
                print(f"Token已过期，用户: {username}")

            from ui.login_window import LoginWindow
            login_window = LoginWindow()
            login_window.show()

        # 运行应用程序
        sys.exit(app.exec())

    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保已安装PyQt6: pip install PyQt6")
        input("按回车键退出...")

    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    main()
